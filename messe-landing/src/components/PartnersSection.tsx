type PartnersSectionProps = {
  className?: string;
};

export default function PartnersSection({ className = '' }: PartnersSectionProps) {
  // Create array of partner logos with different gradient variations
  const partners = Array.from({ length: 48 }, (_, i) => ({
    id: i,
    gradient: i % 4 === 0 ? 'from-orange-400 to-yellow-400' :
              i % 4 === 1 ? 'from-orange-500 to-yellow-500' :
              i % 4 === 2 ? 'from-orange-300 to-yellow-300' :
              'from-orange-600 to-yellow-600'
  }));

  return (
    <section className={`py-12 bg-white ${className}`}>
      <div className="max-w-7xl mx-auto px-4">
        {/* Expoglobal Logo Grid */}
        <div className="grid grid-cols-8 md:grid-cols-12 lg:grid-cols-12 gap-3 items-center justify-items-center">
          {partners.map((partner) => (
            <div
              key={partner.id}
              className={`w-12 h-12 md:w-14 md:h-14 bg-gradient-to-br ${partner.gradient} rounded-lg flex items-center justify-center hover:scale-105 transition-transform duration-200`}
            >
              <div className="w-8 h-8 md:w-10 md:h-10 bg-white rounded opacity-90 flex items-center justify-center">
                <div className="w-4 h-4 md:w-6 md:h-6 bg-gray-400 rounded"></div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional partner logos section */}
        <div className="mt-8 grid grid-cols-6 md:grid-cols-10 lg:grid-cols-12 gap-4 items-center justify-items-center opacity-60">
          {Array.from({ length: 36 }, (_, i) => (
            <div
              key={`extra-${i}`}
              className="w-10 h-10 md:w-12 md:h-12 bg-gray-200 rounded-lg flex items-center justify-center"
            >
              <div className="w-6 h-6 md:w-8 md:h-8 bg-gray-300 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
