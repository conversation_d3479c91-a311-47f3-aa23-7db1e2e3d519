type Advantage = {
  id: number;
  title: string;
  description: string;
  icon: string;
};

import { Link } from 'react-router-dom';

type AdvantagesSectionProps = {
  className?: string;
};

export default function AdvantagesSection({ className = '' }: AdvantagesSectionProps) {
  const advantages: Advantage[] = [
    {
      id: 1,
      title: "Title",
      description: "3-4 lines description of the advantage. 3-4 lines description of the advantage.",
      icon: "🎯"
    },
    {
      id: 2,
      title: "Title",
      description: "3-4 lines description of the advantage. 3-4 lines description of the advantage.",
      icon: "⚡"
    },
    {
      id: 3,
      title: "Title",
      description: "3-4 lines description of the advantage. 3-4 lines description of the advantage.",
      icon: "🏆"
    },
  ];

  return (
    <section className={`py-16 bg-background ${className}`}>
      <div className="max-w-container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-block bg-gray-custom rounded-custom px-8 py-4 mb-8">
            <h2 className="text-5xl font-bold text-black">Why Choose Us?</h2>
          </div>
        </div>

        {/* Advantages Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-10 mb-12">
          {advantages.map((advantage) => (
            <div key={advantage.id} className="bg-gray-100 rounded-custom p-6 flex flex-col">
              {/* Icon */}
              <div className="bg-white rounded-custom w-16 h-16 flex items-center justify-center mb-6">
                <span className="text-2xl">{advantage.icon}</span>
              </div>
              
              {/* Content */}
              <div className="flex-1 space-y-4">
                <h3 className="text-2xl font-bold text-black">{advantage.title}</h3>
                <p className="text-xl font-medium text-black leading-relaxed">
                  {advantage.description}
                </p>
              </div>

              {/* Learn More Link */}
              <div className="flex items-center gap-2 mt-6">
                <span className="text-xl font-bold text-black">Learn more</span>
                <svg className="w-5 h-5 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Button */}
        <div className="text-center">
          <Link
            to="/about"
            className="inline-block border-2 border-black text-black font-bold text-xl px-10 py-5 rounded-lg hover:bg-black hover:text-white transition-all duration-300"
          >
            Get to Know Us Better
          </Link>
        </div>
      </div>
    </section>
  );
}
