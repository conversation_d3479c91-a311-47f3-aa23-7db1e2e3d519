import { Link } from 'react-router-dom';

type FooterProps = {
  className?: string;
};

export default function Footer({ className = '' }: FooterProps) {
  return (
    <footer className={`bg-white py-12 ${className}`}>
      <div className="max-w-container mx-auto px-4">
        <div className="flex flex-col md:flex-row items-center justify-between gap-8">
          {/* Left Side - Logo and Navigation */}
          <div className="flex flex-col md:flex-row items-center gap-8">
            {/* Logo */}
            <Link to="/" className="bg-background rounded-custom px-6 py-3 hover:bg-opacity-80 transition-colors">
              <span className="text-2xl font-bold text-black">logo</span>
            </Link>

            {/* Navigation */}
            <nav className="flex flex-col md:flex-row items-center gap-6">
              <Link to="/projects" className="text-2xl font-bold text-black hover:text-[#656CAF] transition-colors">
                Projects
              </Link>
              <Link to="/about" className="text-2xl font-bold text-black hover:text-[#656CAF] transition-colors">
                About Us
              </Link>
              <span className="text-2xl font-bold text-black">
                Expoglobal group
              </span>
            </nav>
          </div>

          {/* Right Side - Contact and Social */}
          <div className="flex flex-col md:flex-row items-center gap-6">
            {/* Social Icons */}
            <div className="flex gap-4">
              <div className="w-8 h-8 bg-[#D9D9D9] rounded"></div>
              <div className="w-8 h-8 bg-[#D9D9D9] rounded"></div>
              <div className="w-8 h-8 bg-[#D9D9D9] rounded"></div>
            </div>

            {/* Header Image */}
            <div className="w-12 h-12 bg-[#D9D9D9] rounded"></div>
          </div>
        </div>

        {/* Chat Icon */}
        <div className="fixed bottom-6 right-6">
          <div className="w-12 h-12 bg-[#656CAF] rounded-full flex items-center justify-center cursor-pointer hover:scale-110 transition-transform shadow-lg">
            <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12c0 1.54.36 3.04 1.05 4.35L2 22l5.65-1.05C9.96 21.64 11.46 22 13 22h7c1.1 0 2-.9 2-2V12c0-5.52-4.48-10-10-10z"/>
            </svg>
          </div>
        </div>
      </div>
    </footer>
  );
}
