type ClientsSectionProps = {
  className?: string;
};

export default function ClientsSection({ className = '' }: ClientsSectionProps) {
  // Mock client logos
  const clients = Array(20).fill(null).map((_, index) => ({
    id: index + 1,
    name: `Client ${index + 1}`,
  }));

  return (
    <section className={`py-16 bg-[#E9E9E9] ${className}`}>
      <div className="max-w-[1360px] mx-auto px-4">
        {/* Section Title */}
        <h2 className="text-3xl lg:text-4xl font-bold text-black text-center mb-12">
          Our Projects & Clients
        </h2>

        {/* Clients Grid - First Row */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 lg:gap-7 mb-4 lg:mb-7">
          {clients.slice(0, 10).map((client) => (
            <div
              key={client.id}
              className="bg-white rounded-lg p-4 lg:p-6 flex items-center justify-center h-16 lg:h-20 shadow-sm hover:shadow-md transition-shadow"
            >
              <span className="text-lg lg:text-2xl font-bold text-black">logo</span>
            </div>
          ))}
        </div>

        {/* Clients Grid - Second Row */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4 lg:gap-7">
          {clients.slice(10, 20).map((client) => (
            <div
              key={client.id}
              className="bg-white rounded-lg p-4 lg:p-6 flex items-center justify-center h-16 lg:h-20 shadow-sm hover:shadow-md transition-shadow"
            >
              <span className="text-lg lg:text-2xl font-bold text-black">logo</span>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
