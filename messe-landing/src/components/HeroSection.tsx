type HeroSectionProps = {
  className?: string;
};

export default function HeroSection({ className = '' }: HeroSectionProps) {
  return (
    <section className={`bg-gradient-to-b from-gray-200 to-gray-400 py-20 lg:py-32 ${className}`}>
      <div className="max-w-[1360px] mx-auto px-4 flex flex-col items-center justify-center min-h-[500px] lg:min-h-[600px]">
        {/* Play Button */}
        <div className="mb-12">
          <div className="w-20 h-20 lg:w-24 lg:h-24 bg-white rounded-lg flex items-center justify-center shadow-lg cursor-pointer hover:scale-105 transition-transform">
            <svg className="w-10 h-10 lg:w-12 lg:h-12 text-black ml-1" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </div>
        </div>

        {/* CTA Button */}
        <button className="bg-[#656CAF] text-white font-bold text-lg lg:text-xl px-8 lg:px-10 py-4 lg:py-5 rounded-lg hover:bg-opacity-90 transition-all duration-300 shadow-lg">
          Discuss Your Project
        </button>
      </div>
    </section>
  );
}
