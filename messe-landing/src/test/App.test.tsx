import { render, screen } from '@testing-library/react'
import { describe, it, expect } from 'vitest'
import { BrowserRouter } from 'react-router-dom'
import App from '../App'

const renderWithRouter = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  )
}

describe('App', () => {
  it('renders home page by default', () => {
    renderWithRouter(<App />)

    // Check if main sections are rendered on home page
    expect(screen.getByText('Our Projects & Clients')).toBeInTheDocument()
    expect(screen.getByText('Our Collaboration Process')).toBeInTheDocument()
    expect(screen.getByText('Why Choose Us?')).toBeInTheDocument()
    expect(screen.getByText("We're always happy to help")).toBeInTheDocument()
  })

  it('renders CTA buttons on home page', () => {
    renderWithRouter(<App />)

    const discussButtons = screen.getAllByText('Discuss Your Project')
    expect(discussButtons.length).toBeGreaterThan(0)

    const allProjectsButtons = screen.getAllByText('All Projects')
    expect(allProjectsButtons.length).toBeGreaterThan(0)

    const knowUsButtons = screen.getAllByText('Get to Know Us Better')
    expect(knowUsButtons.length).toBeGreaterThan(0)
  })

  it('renders contact form on home page', () => {
    renderWithRouter(<App />)

    // Form is now in footer with placeholder text instead of labels
    const nameInputs = screen.getAllByPlaceholderText(/name/i)
    expect(nameInputs.length).toBeGreaterThan(0)

    const phoneInputs = screen.getAllByPlaceholderText(/phone/i)
    expect(phoneInputs.length).toBeGreaterThan(0)

    const emailInputs = screen.getAllByPlaceholderText(/email/i)
    expect(emailInputs.length).toBeGreaterThan(0)

    const sendButtons = screen.getAllByRole('button', { name: /send/i })
    expect(sendButtons.length).toBeGreaterThan(0)
  })
})
